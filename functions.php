<?php
/**
 * Camí Cycling Tours Theme Functions
 * 
 * @package CamiCycling
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function cami_cycling_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Load text domain for translations
    load_theme_textdomain('cami-cycling', get_template_directory() . '/languages');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'cami-cycling'),
        'footer' => __('Footer Menu', 'cami-cycling'),
    ));
}
add_action('after_setup_theme', 'cami_cycling_setup');

/**
 * Enqueue Scripts and Styles
 */
function cami_cycling_scripts() {
    // Main stylesheet
    wp_enqueue_style('cami-cycling-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Additional CSS for booking form and mobile styles
    wp_enqueue_style('cami-cycling-additional', get_template_directory_uri() . '/assets/css/additional-styles.css', array('cami-cycling-style'), '1.0.0');
    
    // Main JavaScript
    wp_enqueue_script('cami-cycling-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('cami-cycling-main', 'cami_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('cami_nonce'),
        'strings' => array(
            'booking_success' => __('Thank you! Your booking request has been submitted successfully.', 'cami-cycling'),
            'booking_error' => __('Sorry, there was an error submitting your booking. Please try again.', 'cami-cycling'),
        )
    ));
}
add_action('wp_enqueue_scripts', 'cami_cycling_scripts');

/**
 * Handle Booking Form Submission
 */
function handle_booking_submission() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['booking_nonce'], 'booking_nonce')) {
        wp_die(__('Security check failed', 'cami-cycling'));
    }
    
    // Sanitize form data
    $tour_type = sanitize_text_field($_POST['tour_type']);
    $route = sanitize_text_field($_POST['route']);
    $start_date = sanitize_text_field($_POST['start_date']);
    $group_size = sanitize_text_field($_POST['group_size']);
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Validate required fields
    if (empty($tour_type) || empty($route) || empty($start_date) || empty($group_size) || empty($name) || empty($email)) {
        wp_redirect(add_query_arg('booking', 'error', wp_get_referer()));
        exit;
    }
    
    // Prepare email content
    $subject = sprintf(__('New Booking Request - %s', 'cami-cycling'), $name);
    $admin_email = get_option('admin_email');
    
    $email_content = sprintf(
        __("New booking request received:\n\nTour Type: %s\nRoute: %s\nStart Date: %s\nGroup Size: %s\nName: %s\nEmail: %s\nPhone: %s\nMessage: %s", 'cami-cycling'),
        $tour_type,
        $route,
        $start_date,
        $group_size,
        $name,
        $email,
        $phone,
        $message
    );
    
    // Send email
    $mail_sent = wp_mail($admin_email, $subject, $email_content);
    
    // Send confirmation email to customer
    $customer_subject = __('Booking Confirmation - Camí Cycling Tours', 'cami-cycling');
    $customer_content = sprintf(
        __("Dear %s,\n\nThank you for your booking request! We have received your inquiry for:\n\nTour: %s\nRoute: %s\nDate: %s\nGroup Size: %s\n\nWe will respond within 24 hours with availability and detailed itinerary.\n\nBest regards,\nCamí Cycling Tours Team", 'cami-cycling'),
        $name,
        $tour_type,
        $route,
        $start_date,
        $group_size
    );
    
    wp_mail($email, $customer_subject, $customer_content);
    
    // Redirect with success message
    if ($mail_sent) {
        wp_redirect(add_query_arg('booking', 'success', wp_get_referer()));
    } else {
        wp_redirect(add_query_arg('booking', 'error', wp_get_referer()));
    }
    exit;
}
add_action('admin_post_process_booking', 'handle_booking_submission');
add_action('admin_post_nopriv_process_booking', 'handle_booking_submission');

/**
 * Add Custom Body Classes
 */
function cami_cycling_body_classes($classes) {
    if (is_front_page()) {
        $classes[] = 'homepage';
    }
    return $classes;
}
add_filter('body_class', 'cami_cycling_body_classes');

/**
 * Customize Excerpt Length
 */
function cami_cycling_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'cami_cycling_excerpt_length');

/**
 * Custom Excerpt More
 */
function cami_cycling_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'cami_cycling_excerpt_more');

/**
 * Add SVG Support
 */
function cami_cycling_mime_types($mimes) {
    $mimes['svg'] = 'image/svg+xml';
    return $mimes;
}
add_filter('upload_mimes', 'cami_cycling_mime_types');

/**
 * Optimize WordPress for Performance
 */
function cami_cycling_optimize() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'cami_cycling_optimize');

/**
 * Add Structured Data for SEO
 */
function cami_cycling_structured_data() {
    if (is_front_page()) {
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'TouristAttraction',
            'name' => 'Camí Cycling Tours',
            'description' => __('Premium cycling tours in Catalunya with Minimalist Landscape Geometry design', 'cami-cycling'),
            'url' => home_url(),
            'telephone' => '+34 93 XXX XXXX',
            'email' => '<EMAIL>',
            'address' => array(
                '@type' => 'PostalAddress',
                'addressLocality' => 'Barcelona',
                'addressRegion' => 'Catalunya',
                'addressCountry' => 'ES'
            ),
            'geo' => array(
                '@type' => 'GeoCoordinates',
                'latitude' => '41.3851',
                'longitude' => '2.1734'
            ),
            'priceRange' => '€65-320',
            'touristType' => 'Cycling enthusiasts'
        );
        
        echo '<script type="application/ld+json">' . json_encode($structured_data) . '</script>';
    }
}
add_action('wp_head', 'cami_cycling_structured_data');

/**
 * Security Enhancements
 */
function cami_cycling_security() {
    // Hide WordPress version
    remove_action('wp_head', 'wp_generator');
    
    // Remove version from scripts and styles
    function remove_version_scripts_styles($src) {
        if (strpos($src, 'ver=')) {
            $src = remove_query_arg('ver', $src);
        }
        return $src;
    }
    add_filter('style_loader_src', 'remove_version_scripts_styles', 9999);
    add_filter('script_loader_src', 'remove_version_scripts_styles', 9999);
}
add_action('init', 'cami_cycling_security');

/**
 * Custom Post Types (if needed for tours/routes)
 */
function cami_cycling_custom_post_types() {
    // Register Tours Post Type
    register_post_type('tours', array(
        'labels' => array(
            'name' => __('Tours', 'cami-cycling'),
            'singular_name' => __('Tour', 'cami-cycling'),
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-location-alt',
    ));
    
    // Register Routes Post Type
    register_post_type('routes', array(
        'labels' => array(
            'name' => __('Routes', 'cami-cycling'),
            'singular_name' => __('Route', 'cami-cycling'),
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-location',
    ));
}
add_action('init', 'cami_cycling_custom_post_types');

/**
 * Widget Areas
 */
function cami_cycling_widgets_init() {
    register_sidebar(array(
        'name' => __('Footer Widget Area', 'cami-cycling'),
        'id' => 'footer-widgets',
        'description' => __('Widgets in this area will be shown in the footer.', 'cami-cycling'),
        'before_widget' => '<div class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="widget-title">',
        'after_title' => '</h4>',
    ));
}
add_action('widgets_init', 'cami_cycling_widgets_init');
