<?php
/**
 * Search Results Template
 * 
 * @package CamiCycling
 */

get_header(); ?>

<main id="main" class="site-main search-results">
    <section class="section search-section">
        <div class="container">
            <header class="search-header">
                <h1>
                    <?php
                    printf(
                        __('Search Results for: %s', 'cami-cycling'),
                        '<span class="search-term">' . get_search_query() . '</span>'
                    );
                    ?>
                </h1>
                <p class="search-subtitle">
                    <?php
                    global $wp_query;
                    if ($wp_query->found_posts) {
                        printf(
                            _n(
                                'Found %d result',
                                'Found %d results',
                                $wp_query->found_posts,
                                'cami-cycling'
                            ),
                            $wp_query->found_posts
                        );
                    } else {
                        _e('No results found. Try a different search term.', 'cami-cycling');
                    }
                    ?>
                </p>
            </header>

            <?php if (have_posts()) : ?>
                <div class="search-results-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        <article class="search-result-item">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="result-thumbnail">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <div class="result-content">
                                <h2 class="result-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h2>
                                
                                <div class="result-meta">
                                    <span class="result-date"><?php echo get_the_date(); ?></span>
                                    <span class="result-type"><?php echo get_post_type(); ?></span>
                                </div>
                                
                                <div class="result-excerpt">
                                    <?php the_excerpt(); ?>
                                </div>
                                
                                <a href="<?php the_permalink(); ?>" class="result-link">
                                    <?php _e('Read More', 'cami-cycling'); ?>
                                </a>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>

                <?php
                // Pagination
                the_posts_pagination(array(
                    'prev_text' => __('Previous', 'cami-cycling'),
                    'next_text' => __('Next', 'cami-cycling'),
                ));
                ?>

            <?php else : ?>
                <div class="no-results">
                    <div class="no-results-content">
                        <h2><?php _e('Nothing Found', 'cami-cycling'); ?></h2>
                        <p><?php _e('Sorry, but nothing matched your search terms. Please try again with different keywords.', 'cami-cycling'); ?></p>
                        
                        <div class="search-suggestions">
                            <h3><?php _e('Search Suggestions:', 'cami-cycling'); ?></h3>
                            <ul>
                                <li><?php _e('Try different keywords', 'cami-cycling'); ?></li>
                                <li><?php _e('Check your spelling', 'cami-cycling'); ?></li>
                                <li><?php _e('Use more general terms', 'cami-cycling'); ?></li>
                                <li><?php _e('Try fewer keywords', 'cami-cycling'); ?></li>
                            </ul>
                        </div>
                        
                        <div class="new-search">
                            <?php get_search_form(); ?>
                        </div>
                        
                        <div class="popular-content">
                            <h3><?php _e('Popular Tours', 'cami-cycling'); ?></h3>
                            <div class="popular-links">
                                <a href="#tours" class="btn btn-secondary"><?php _e('View All Tours', 'cami-cycling'); ?></a>
                                <a href="#routes" class="btn btn-secondary"><?php _e('Explore Routes', 'cami-cycling'); ?></a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</main>

<style>
.search-section {
    padding: 3rem 0 5rem;
}

.search-header {
    text-align: center;
    margin-bottom: 3rem;
}

.search-header h1 {
    font-size: 2.5rem;
    color: var(--stone-grey);
    margin-bottom: 1rem;
}

.search-term {
    color: var(--sage-green);
    font-style: italic;
}

.search-subtitle {
    font-size: 1.1rem;
    color: var(--stone-grey-light);
}

.search-results-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.search-result-item {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 2rem;
    padding: 2rem;
    background: var(--warm-white);
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(138, 137, 136, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.search-result-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(138, 137, 136, 0.15);
}

.result-thumbnail img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

.result-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.result-title a {
    color: var(--stone-grey);
    text-decoration: none;
    transition: color 0.3s ease;
}

.result-title a:hover {
    color: var(--sage-green);
}

.result-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--stone-grey-light);
}

.result-excerpt {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--stone-grey);
}

.result-link {
    color: var(--sage-green);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.result-link:hover {
    color: var(--sage-green-hover);
}

.no-results {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.no-results-content {
    padding: 3rem 2rem;
    background: rgba(135, 169, 107, 0.05);
    border-radius: 16px;
}

.search-suggestions {
    margin: 2rem 0;
    text-align: left;
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
}

.search-suggestions li {
    padding: 0.5rem 0;
    color: var(--stone-grey);
}

.search-suggestions li::before {
    content: "→";
    color: var(--sage-green);
    margin-right: 0.5rem;
}

.new-search {
    margin: 2rem 0;
}

.new-search form {
    display: flex;
    gap: 1rem;
    justify-content: center;
    max-width: 400px;
    margin: 0 auto;
}

.new-search input[type="search"] {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(138, 137, 136, 0.2);
    border-radius: 6px;
    font-family: 'Montserrat', sans-serif;
}

.new-search input[type="submit"] {
    padding: 0.75rem 1.5rem;
    background: var(--sage-green);
    color: var(--warm-white);
    border: none;
    border-radius: 6px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    cursor: pointer;
}

.popular-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .search-result-item {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .result-thumbnail img {
        height: 200px;
    }
    
    .new-search form {
        flex-direction: column;
    }
    
    .popular-links {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<?php get_footer(); ?>
