/* Additional Styles for Camí Cycling Tours */

/* Booking Section */
.booking-section {
  background: linear-gradient(135deg, var(--warm-white) 0%, #f8f7f5 100%);
  position: relative;
}

.booking-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(45deg, transparent 40%, rgba(135, 169, 107, 0.02) 50%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, rgba(135, 169, 107, 0.02) 50%, transparent 60%);
  pointer-events: none;
}

.booking-form-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.booking-form {
  background: var(--warm-white);
  padding: 3rem;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(138, 137, 136, 0.1);
  border: 1px solid rgba(135, 169, 107, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: var(--stone-grey);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 2px solid rgba(138, 137, 136, 0.2);
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 1rem;
  background: var(--warm-white);
  color: var(--stone-grey);
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--sage-green);
  box-shadow: 0 0 0 3px rgba(135, 169, 107, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-submit {
  text-align: center;
  margin-top: 2rem;
}

.btn-large {
  padding: 1.25rem 3rem;
  font-size: 1.1rem;
  font-weight: 700;
}

.booking-note {
  margin-top: 1rem;
  font-size: 0.9rem;
  color: var(--stone-grey-light);
  font-style: italic;
}

/* Trust Signals */
.trust-signals {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(138, 137, 136, 0.1);
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--stone-grey);
  font-size: 0.9rem;
  font-weight: 500;
}

.trust-item svg {
  flex-shrink: 0;
}

/* Footer Styles */
.site-footer {
  background: var(--stone-grey);
  color: var(--warm-white);
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-section h4 {
  color: var(--warm-white);
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  letter-spacing: 0.02em;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.75rem;
}

.footer-section a {
  color: rgba(254, 253, 251, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--warm-white);
}

.footer-logo .logo {
  color: var(--warm-white);
  margin-bottom: 1rem;
}

.footer-description {
  color: rgba(254, 253, 251, 0.7);
  line-height: 1.7;
  font-size: 0.95rem;
}

.contact-info p {
  margin-bottom: 0.5rem;
  color: rgba(254, 253, 251, 0.8);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(254, 253, 251, 0.1);
  border-radius: 50%;
  color: rgba(254, 253, 251, 0.8);
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--sage-green);
  color: var(--warm-white);
  transform: translateY(-2px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(254, 253, 251, 0.1);
  color: rgba(254, 253, 251, 0.6);
  font-size: 0.9rem;
}

.footer-legal {
  display: flex;
  gap: 2rem;
}

.footer-legal a {
  color: rgba(254, 253, 251, 0.6);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-legal a:hover {
  color: var(--warm-white);
}

/* Mobile Menu Styles */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--stone-grey);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(254, 253, 251, 0.98);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
}

.mobile-nav {
  text-align: center;
}

.mobile-nav ul {
  list-style: none;
  margin-bottom: 3rem;
}

.mobile-nav ul li {
  margin-bottom: 2rem;
}

.mobile-nav a {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--stone-grey);
  text-decoration: none;
  transition: color 0.3s ease;
}

.mobile-nav a:hover {
  color: var(--sage-green);
}

.mobile-language-selector h4 {
  color: var(--stone-grey);
  margin-bottom: 1rem;
}

.mobile-language-selector a {
  display: inline-block;
  margin: 0 1rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.mobile-language-selector a.active,
.mobile-language-selector a:hover {
  background: var(--sage-green);
  color: var(--warm-white);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  
  .trust-signals {
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  /* Mobile Navigation */
  .main-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  /* About Section */
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .about-stats {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .about-img {
    height: 300px;
  }

  /* Booking Form */
  .booking-form {
    padding: 2rem 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .trust-signals {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  /* Footer */
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-legal {
    justify-content: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .booking-form {
    padding: 1.5rem 1rem;
  }
  
  .btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
  
  .footer-legal {
    flex-direction: column;
    gap: 1rem;
  }
}

/* About Section */
.about-section {
  background: var(--warm-white);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text .lead {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--sage-green);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.about-text p {
  color: var(--stone-grey);
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.stat {
  text-align: center;
  padding: 1.5rem;
  background: rgba(135, 169, 107, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(135, 169, 107, 0.1);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--sage-green);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--stone-grey);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.about-image {
  position: relative;
}

.about-img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(138, 137, 136, 0.15);
}

.about-image::before {
  content: '';
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: var(--sage-green);
  opacity: 0.1;
  border-radius: 50%;
  z-index: -1;
}

/* Booking Success/Error Messages */
.booking-message {
  padding: 1rem 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  font-weight: 500;
  text-align: center;
}

.booking-message.success {
  background: rgba(135, 169, 107, 0.1);
  color: var(--sage-green);
  border: 1px solid rgba(135, 169, 107, 0.3);
}

.booking-message.error {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--sage-green);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
