/**
 * Camí Cycling Tours - Main JavaScript
 */

(function($) {
    'use strict';

    // DOM Ready
    $(document).ready(function() {
        initializeComponents();
    });

    /**
     * Initialize all components
     */
    function initializeComponents() {
        initSmoothScrolling();
        initMobileMenu();
        initBookingForm();
        initLanguageSelector();
        initHeaderScroll();
        initGeometricAnimations();
        showBookingMessages();
    }

    /**
     * Smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            var target = $(this.getAttribute('href'));
            if (target.length) {
                var headerHeight = $('.site-header').outerHeight();
                var targetPosition = target.offset().top - headerHeight - 20;
                
                $('html, body').animate({
                    scrollTop: targetPosition
                }, 800, 'swing');
            }
        });
    }

    /**
     * Mobile menu functionality
     */
    function initMobileMenu() {
        var $toggle = $('.mobile-menu-toggle');
        var $overlay = $('.mobile-nav-overlay');
        var $body = $('body');

        $toggle.on('click', function() {
            $overlay.fadeToggle(300);
            $body.toggleClass('mobile-menu-open');
            
            // Animate hamburger icon
            $(this).toggleClass('active');
        });

        // Close menu when clicking on links
        $('.mobile-nav a').on('click', function() {
            $overlay.fadeOut(300);
            $body.removeClass('mobile-menu-open');
            $toggle.removeClass('active');
        });

        // Close menu when clicking overlay
        $overlay.on('click', function(e) {
            if (e.target === this) {
                $(this).fadeOut(300);
                $body.removeClass('mobile-menu-open');
                $toggle.removeClass('active');
            }
        });
    }

    /**
     * Booking form functionality
     */
    function initBookingForm() {
        var $form = $('#booking-form');
        
        if ($form.length) {
            // Form validation
            $form.on('submit', function(e) {
                if (!validateBookingForm()) {
                    e.preventDefault();
                    return false;
                }
                
                // Add loading state
                var $submitBtn = $form.find('button[type="submit"]');
                $submitBtn.addClass('loading').prop('disabled', true);
                $submitBtn.text('Processing...');
            });

            // Dynamic pricing display
            $('#tour-type').on('change', function() {
                updatePricingInfo($(this).val());
            });

            // Date validation
            $('#start-date').on('change', function() {
                validateDate($(this).val());
            });
        }
    }

    /**
     * Validate booking form
     */
    function validateBookingForm() {
        var isValid = true;
        var $form = $('#booking-form');
        
        // Remove previous error states
        $form.find('.error').removeClass('error');
        
        // Required fields
        var requiredFields = ['tour-type', 'route', 'start-date', 'group-size', 'name', 'email'];
        
        requiredFields.forEach(function(fieldId) {
            var $field = $('#' + fieldId);
            if (!$field.val().trim()) {
                $field.addClass('error');
                isValid = false;
            }
        });

        // Email validation
        var email = $('#email').val();
        if (email && !isValidEmail(email)) {
            $('#email').addClass('error');
            isValid = false;
        }

        // Date validation
        var startDate = $('#start-date').val();
        if (startDate && new Date(startDate) < new Date()) {
            $('#start-date').addClass('error');
            isValid = false;
        }

        if (!isValid) {
            showNotification('Please fill in all required fields correctly.', 'error');
        }

        return isValid;
    }

    /**
     * Email validation
     */
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Date validation
     */
    function validateDate(dateString) {
        var selectedDate = new Date(dateString);
        var today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            showNotification('Please select a future date.', 'error');
            return false;
        }
        return true;
    }

    /**
     * Update pricing information based on tour type
     */
    function updatePricingInfo(tourType) {
        var pricingInfo = {
            'one-day': '€65-85 per person',
            'multi-day': '€180-280 per person',
            'expedition': '€200-320 per person'
        };

        if (pricingInfo[tourType]) {
            // You can add a pricing display element if needed
            console.log('Selected tour pricing:', pricingInfo[tourType]);
        }
    }

    /**
     * Language selector functionality
     */
    function initLanguageSelector() {
        $('.language-selector a, .mobile-language-selector a').on('click', function(e) {
            e.preventDefault();
            
            var lang = $(this).data('lang');
            if (lang) {
                // Remove active class from all language links
                $('.language-selector a, .mobile-language-selector a').removeClass('active');
                
                // Add active class to clicked language
                $('[data-lang="' + lang + '"]').addClass('active');
                
                // Here you would typically redirect to the language-specific URL
                // For now, we'll just update the content dynamically
                updateLanguageContent(lang);
            }
        });
    }

    /**
     * Update content based on selected language
     */
    function updateLanguageContent(lang) {
        // This is a simplified example - in a real implementation,
        // you would use WordPress's built-in internationalization
        var translations = {
            'en_US': {
                'hero-title': 'Discover Catalunya Through Local Eyes',
                'hero-subtitle': 'Premium cycling experiences where geometry meets geography'
            },
            'es_ES': {
                'hero-title': 'Descubre Cataluña con Ojos Locales',
                'hero-subtitle': 'Experiencias premium de ciclismo donde la geometría se encuentra con la geografía'
            },
            'ca': {
                'hero-title': 'Descobreix Catalunya amb Ulls Locals',
                'hero-subtitle': 'Experiències premium de ciclisme on la geometria es troba amb la geografia'
            },
            'ru_RU': {
                'hero-title': 'Откройте Каталонию Глазами Местных',
                'hero-subtitle': 'Премиальные велосипедные туры, где геометрия встречается с географией'
            }
        };

        if (translations[lang]) {
            // Update hero content
            $('.hero h1').text(translations[lang]['hero-title']);
            $('.hero-subtitle').text(translations[lang]['hero-subtitle']);
        }
    }

    /**
     * Header scroll effects
     */
    function initHeaderScroll() {
        var $header = $('.site-header');
        var lastScrollTop = 0;

        $(window).on('scroll', function() {
            var scrollTop = $(this).scrollTop();
            
            // Add/remove scrolled class
            if (scrollTop > 100) {
                $header.addClass('scrolled');
            } else {
                $header.removeClass('scrolled');
            }

            // Hide/show header on scroll
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                $header.addClass('header-hidden');
            } else {
                $header.removeClass('header-hidden');
            }
            
            lastScrollTop = scrollTop;
        });
    }

    /**
     * Geometric animations
     */
    function initGeometricAnimations() {
        // Animate geometric elements on scroll
        $(window).on('scroll', function() {
            var scrolled = $(window).scrollTop();
            var rate = scrolled * -0.5;
            
            $('.geometric-overlay .triangle-1').css('transform', 'translateY(' + rate + 'px)');
            $('.geometric-overlay .triangle-2').css('transform', 'translateY(' + (rate * 0.8) + 'px)');
            $('.geometric-overlay .triangle-3').css('transform', 'translateY(' + (rate * 1.2) + 'px)');
        });

        // Intersection Observer for fade-in animations
        if ('IntersectionObserver' in window) {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1
            });

            // Observe elements for animation
            $('.tier-card, .route-card, .testimonial-card').each(function() {
                observer.observe(this);
            });
        }
    }

    /**
     * Show booking messages
     */
    function showBookingMessages() {
        var urlParams = new URLSearchParams(window.location.search);
        var bookingStatus = urlParams.get('booking');
        
        if (bookingStatus) {
            var message = '';
            var type = '';
            
            if (bookingStatus === 'success') {
                message = cami_ajax.strings.booking_success;
                type = 'success';
            } else if (bookingStatus === 'error') {
                message = cami_ajax.strings.booking_error;
                type = 'error';
            }
            
            if (message) {
                showNotification(message, type);
                
                // Remove the parameter from URL
                var newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }
        }
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        var $notification = $('<div class="booking-message ' + type + '">' + message + '</div>');
        
        // Insert before booking form or at top of page
        var $target = $('.booking-form-container');
        if ($target.length) {
            $target.prepend($notification);
        } else {
            $('body').prepend($notification);
        }
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Utility function to debounce events
     */
    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // Debounced scroll handler
    var debouncedScroll = debounce(function() {
        // Additional scroll handling if needed
    }, 10);

    $(window).on('scroll', debouncedScroll);

})(jQuery);
