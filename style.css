/*
Theme Name: Camí Cycling Tours
Description: Premium cycling tours website with Minimalist Landscape Geometry design concept. Sophisticated geometric abstraction of Catalonian landscapes with mathematical precision and timeless appeal.
Version: 1.0
Author: <PERSON><PERSON> Cycling Tours
Text Domain: cami-cycling
*/

/* Import Montserrat Font */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap');

/* CSS Custom Properties - Color Palette */
:root {
  --sage-green: #87a96b;
  --stone-grey: #8a8988;
  --warm-white: #fefdfb;
  --sage-green-hover: #7a9660;
  --stone-grey-light: #a8a6a5;
  --shadow-light: rgba(138, 137, 136, 0.1);
  --shadow-medium: rgba(138, 137, 136, 0.2);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Montserrat', sans-serif;
  background-color: var(--warm-white);
  color: var(--stone-grey);
  line-height: 1.6;
  font-size: 16px;
}

/* Typography System */
h1, h2, h3, h4, h5, h6 {
  font-family: '<PERSON><PERSON>rat', sans-serif;
  font-weight: 700;
  letter-spacing: 0.02em;
  margin-bottom: 1rem;
  color: var(--stone-grey);
}

h1 {
  font-size: 3.5rem;
  font-weight: 800;
  letter-spacing: 0.03em;
  line-height: 1.1;
}

h2 {
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 0.025em;
}

h3 {
  font-size: 1.8rem;
  font-weight: 600;
}

p {
  margin-bottom: 1.5rem;
  font-weight: 400;
}

/* Container and Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 5rem 0;
}

/* Geometric Elements */
.geometric-triangle {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0.1;
}

.geometric-triangle::before {
  content: '';
  position: absolute;
  border-style: solid;
}

.triangle-large {
  border-left: 100px solid transparent;
  border-right: 100px solid transparent;
  border-bottom: 173px solid var(--sage-green);
}

.triangle-medium {
  border-left: 60px solid transparent;
  border-right: 60px solid transparent;
  border-bottom: 104px solid var(--sage-green);
}

.triangle-small {
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 52px solid var(--sage-green);
}

/* Header Styles */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(254, 253, 251, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
  color: var(--stone-grey);
}

.logo-icon {
  width: 40px;
  height: 40px;
  position: relative;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 0.02em;
}

/* Navigation */
.main-nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.main-nav a {
  text-decoration: none;
  color: var(--stone-grey);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.main-nav a:hover {
  color: var(--sage-green);
}

.main-nav a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--sage-green);
  transition: width 0.3s ease;
}

.main-nav a:hover::after {
  width: 100%;
}

/* Language Selector */
.language-selector {
  display: flex;
  gap: 0.5rem;
}

.language-selector a {
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.language-selector a:hover,
.language-selector a.active {
  background-color: var(--sage-green);
  color: var(--warm-white);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.btn-primary {
  background-color: var(--sage-green);
  color: var(--warm-white);
}

.btn-primary:hover {
  background-color: var(--sage-green-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.btn-secondary {
  background-color: transparent;
  color: var(--sage-green);
  border: 2px solid var(--sage-green);
}

.btn-secondary:hover {
  background-color: var(--sage-green);
  color: var(--warm-white);
}

/* Hero Section */
.hero {
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
  background: linear-gradient(135deg, var(--warm-white) 0%, #f8f7f5 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('assets/images/hero-bg.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  padding: 0 2rem;
}

.hero h1 {
  color: var(--stone-grey);
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--stone-grey-light);
  margin-bottom: 3rem;
  letter-spacing: 0.01em;
}

/* Geometric Overlays */
.geometric-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.geometric-overlay .triangle-1 {
  position: absolute;
  top: 10%;
  right: 15%;
  opacity: 0.05;
}

.geometric-overlay .triangle-2 {
  position: absolute;
  bottom: 20%;
  left: 10%;
  opacity: 0.08;
}

.geometric-overlay .triangle-3 {
  position: absolute;
  top: 60%;
  right: 40%;
  opacity: 0.03;
}

/* Service Tiers */
.service-tiers {
  background: var(--warm-white);
}

.tiers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.tier-card {
  background: var(--warm-white);
  border: 2px solid var(--sage-green);
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

.tier-card::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: var(--sage-green);
  opacity: 0.05;
  transform: rotate(45deg);
}

.tier-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px var(--shadow-medium);
}

.tier-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--sage-green);
  margin-bottom: 1rem;
}

.tier-price {
  font-size: 2rem;
  font-weight: 800;
  color: var(--stone-grey);
  margin-bottom: 1.5rem;
}

.tier-description {
  color: var(--stone-grey-light);
  margin-bottom: 2rem;
  line-height: 1.7;
}

/* Routes Showcase */
.routes-showcase {
  background: linear-gradient(135deg, #f8f7f5 0%, var(--warm-white) 100%);
}

.routes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.route-card {
  background: var(--sage-green);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  height: 400px;
  transition: all 0.3s ease;
}

.route-card:hover {
  transform: scale(1.02);
  box-shadow: 0 25px 50px rgba(135, 169, 107, 0.3);
}

.route-image {
  width: 100%;
  height: 60%;
  object-fit: cover;
}

.route-content {
  padding: 2rem;
  color: var(--warm-white);
  position: relative;
}

.route-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--warm-white);
}

.route-description {
  font-size: 0.95rem;
  opacity: 0.9;
  line-height: 1.6;
}

/* Testimonials */
.testimonials {
  background: var(--warm-white);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.testimonial-card {
  background: var(--warm-white);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px var(--shadow-light);
  position: relative;
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px var(--shadow-medium);
}

.testimonial-quote {
  font-style: italic;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 2rem;
  color: var(--stone-grey);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--sage-green);
}

.author-info p {
  font-size: 0.9rem;
  color: var(--stone-grey-light);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .main-nav ul {
    flex-direction: column;
    gap: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section {
    padding: 3rem 0;
  }

  .tiers-grid,
  .routes-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .tier-card {
    padding: 2rem 1.5rem;
  }
}
