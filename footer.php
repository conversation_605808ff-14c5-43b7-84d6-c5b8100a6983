    <!-- Booking Section -->
    <section id="booking" class="section booking-section">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: 1rem;"><?php _e('Book Your Adventure', 'cami-cycling'); ?></h2>
            <p style="text-align: center; color: var(--stone-grey-light); margin-bottom: 3rem; font-size: 1.1rem;">
                <?php _e('Start your geometric journey through Catalunya\'s most beautiful landscapes', 'cami-cycling'); ?>
            </p>
            
            <div class="booking-form-container">
                <form class="booking-form" id="booking-form" method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                    <input type="hidden" name="action" value="process_booking">
                    <?php wp_nonce_field('booking_nonce', 'booking_nonce'); ?>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="tour-type"><?php _e('Tour Type', 'cami-cycling'); ?></label>
                            <select id="tour-type" name="tour_type" required>
                                <option value=""><?php _e('Select your adventure', 'cami-cycling'); ?></option>
                                <option value="one-day"><?php _e('One-Day Adventures (€65-85)', 'cami-cycling'); ?></option>
                                <option value="multi-day"><?php _e('Multi-Day Experiences (€180-280)', 'cami-cycling'); ?></option>
                                <option value="expedition"><?php _e('Extended Expeditions (€200-320)', 'cami-cycling'); ?></option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="route"><?php _e('Preferred Route', 'cami-cycling'); ?></label>
                            <select id="route" name="route" required>
                                <option value=""><?php _e('Choose your region', 'cami-cycling'); ?></option>
                                <option value="ribes-camprodon"><?php _e('Ribes de Freser & Camprodon', 'cami-cycling'); ?></option>
                                <option value="montseny"><?php _e('Montseny & Sant Hilari Sacalm', 'cami-cycling'); ?></option>
                                <option value="garraf"><?php _e('Garraf & Alt Penedès', 'cami-cycling'); ?></option>
                                <option value="barcelona"><?php _e('Barcelona & Collserola', 'cami-cycling'); ?></option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="start-date"><?php _e('Start Date', 'cami-cycling'); ?></label>
                            <input type="date" id="start-date" name="start_date" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="group-size"><?php _e('Group Size', 'cami-cycling'); ?></label>
                            <select id="group-size" name="group_size" required>
                                <option value=""><?php _e('Number of cyclists', 'cami-cycling'); ?></option>
                                <option value="1">1 <?php _e('person', 'cami-cycling'); ?></option>
                                <option value="2">2 <?php _e('people', 'cami-cycling'); ?></option>
                                <option value="3">3 <?php _e('people', 'cami-cycling'); ?></option>
                                <option value="4">4 <?php _e('people', 'cami-cycling'); ?></option>
                                <option value="5+">5+ <?php _e('people', 'cami-cycling'); ?></option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name"><?php _e('Full Name', 'cami-cycling'); ?></label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email"><?php _e('Email Address', 'cami-cycling'); ?></label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone"><?php _e('Phone Number', 'cami-cycling'); ?></label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    
                    <div class="form-group">
                        <label for="message"><?php _e('Special Requests or Questions', 'cami-cycling'); ?></label>
                        <textarea id="message" name="message" rows="4" placeholder="<?php _e('Tell us about your cycling experience, dietary requirements, or any special requests...', 'cami-cycling'); ?>"></textarea>
                    </div>
                    
                    <div class="form-submit">
                        <button type="submit" class="btn btn-primary btn-large">
                            <?php _e('Book Your Adventure', 'cami-cycling'); ?>
                        </button>
                        <p class="booking-note">
                            <?php _e('We\'ll respond within 24 hours with availability and detailed itinerary', 'cami-cycling'); ?>
                        </p>
                    </div>
                </form>
                
                <!-- Trust Signals -->
                <div class="trust-signals">
                    <div class="trust-item">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="#87a96b"/>
                        </svg>
                        <span><?php _e('Secure Booking', 'cami-cycling'); ?></span>
                    </div>
                    <div class="trust-item">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#87a96b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span><?php _e('Free Cancellation', 'cami-cycling'); ?></span>
                    </div>
                    <div class="trust-item">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L13.09 8.26L20 9L14 14L16 21L12 18L8 21L10 14L4 9L10.91 8.26L12 2Z" fill="#87a96b"/>
                        </svg>
                        <span><?php _e('Local Certified Guides', 'cami-cycling'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <a href="<?php echo home_url(); ?>" class="logo">
                            <div class="logo-icon">
                                <svg width="32" height="32" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 5L35 30H5L20 5Z" fill="#87a96b" opacity="0.8"/>
                                    <path d="M15 15L25 30H5L15 15Z" fill="#87a96b" opacity="0.6"/>
                                    <path d="M25 15L35 30H15L25 15Z" fill="#87a96b" opacity="0.4"/>
                                </svg>
                            </div>
                            <span class="logo-text">Camí Cycling Tours</span>
                        </a>
                    </div>
                    <p class="footer-description">
                        <?php _e('Premium cycling experiences where geometry meets geography. Discover Catalunya through local eyes with mathematical precision and authentic cultural immersion.', 'cami-cycling'); ?>
                    </p>
                </div>
                
                <div class="footer-section">
                    <h4><?php _e('Quick Links', 'cami-cycling'); ?></h4>
                    <ul>
                        <li><a href="#tours"><?php _e('Tours', 'cami-cycling'); ?></a></li>
                        <li><a href="#routes"><?php _e('Routes', 'cami-cycling'); ?></a></li>
                        <li><a href="#about"><?php _e('About Us', 'cami-cycling'); ?></a></li>
                        <li><a href="#contact"><?php _e('Contact', 'cami-cycling'); ?></a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4><?php _e('Contact Info', 'cami-cycling'); ?></h4>
                    <div class="contact-info">
                        <p><strong><?php _e('Email:', 'cami-cycling'); ?></strong> <EMAIL></p>
                        <p><strong><?php _e('Phone:', 'cami-cycling'); ?></strong> +34 93 XXX XXXX</p>
                        <p><strong><?php _e('Location:', 'cami-cycling'); ?></strong> <?php _e('Barcelona, Catalunya', 'cami-cycling'); ?></p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4><?php _e('Follow Us', 'cami-cycling'); ?></h4>
                    <div class="social-links">
                        <a href="#" aria-label="Instagram">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="currentColor" stroke-width="2"/>
                                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" stroke="currentColor" stroke-width="2"/>
                                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Facebook">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Strava">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15.387 17.944l-2.089-4.116h-3.065L15.387 24l5.15-10.172h-3.066m-7.008-5.599l2.836 5.598h4.172L10.463 0l-7 13.828h4.172" fill="currentColor"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> Camí Cycling Tours. <?php _e('All rights reserved.', 'cami-cycling'); ?></p>
                <div class="footer-legal">
                    <a href="#"><?php _e('Privacy Policy', 'cami-cycling'); ?></a>
                    <a href="#"><?php _e('Terms of Service', 'cami-cycling'); ?></a>
                    <a href="#"><?php _e('Cancellation Policy', 'cami-cycling'); ?></a>
                </div>
            </div>
        </div>
    </footer>

<?php wp_footer(); ?>
</body>
</html>
