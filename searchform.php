<?php
/**
 * Custom Search Form Template
 * 
 * @package CamiCycling
 */
?>

<form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
    <label for="search-field" class="screen-reader-text">
        <?php _e('Search for:', 'cami-cycling'); ?>
    </label>
    <input 
        type="search" 
        id="search-field" 
        class="search-field" 
        placeholder="<?php _e('Search tours, routes, destinations...', 'cami-cycling'); ?>" 
        value="<?php echo get_search_query(); ?>" 
        name="s" 
        required
    />
    <button type="submit" class="search-submit">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
        </svg>
        <span class="screen-reader-text"><?php _e('Search', 'cami-cycling'); ?></span>
    </button>
</form>

<style>
.search-form {
    display: flex;
    align-items: center;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
}

.search-field {
    flex: 1;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 2px solid rgba(138, 137, 136, 0.2);
    border-radius: 8px;
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    background: var(--warm-white);
    color: var(--stone-grey);
    transition: all 0.3s ease;
}

.search-field:focus {
    outline: none;
    border-color: var(--sage-green);
    box-shadow: 0 0 0 3px rgba(135, 169, 107, 0.1);
}

.search-field::placeholder {
    color: var(--stone-grey-light);
}

.search-submit {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: var(--stone-grey-light);
    transition: color 0.3s ease;
    border-radius: 4px;
}

.search-submit:hover {
    color: var(--sage-green);
    background: rgba(135, 169, 107, 0.1);
}

.search-submit:focus {
    outline: 2px solid var(--sage-green);
    outline-offset: 2px;
}

.screen-reader-text {
    position: absolute !important;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

@media (max-width: 480px) {
    .search-form {
        max-width: 100%;
    }
    
    .search-field {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}
</style>
