<?php
/**
 * 404 Error Page Template
 * 
 * @package CamiCycling
 */

get_header(); ?>

<main id="main" class="site-main error-404">
    <section class="section error-section">
        <div class="container">
            <div class="error-content">
                <div class="error-geometric">
                    <svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <!-- Large geometric 404 design -->
                        <path d="M100 20L180 160H20L100 20Z" fill="#87a96b" opacity="0.1"/>
                        <path d="M70 70L130 160H10L70 70Z" fill="#87a96b" opacity="0.2"/>
                        <path d="M130 70L190 160H70L130 70Z" fill="#87a96b" opacity="0.15"/>
                        
                        <!-- 404 Text integrated into geometric design -->
                        <text x="100" y="120" text-anchor="middle" fill="#8a8988" font-family="Montserrat" font-size="48" font-weight="800">404</text>
                    </svg>
                </div>
                
                <h1><?php _e('Route Not Found', 'cami-cycling'); ?></h1>
                <p class="error-subtitle">
                    <?php _e('It seems you\'ve taken a detour from our carefully planned routes. Let\'s get you back on track.', 'cami-cycling'); ?>
                </p>
                
                <div class="error-actions">
                    <a href="<?php echo home_url(); ?>" class="btn btn-primary">
                        <?php _e('Return to Homepage', 'cami-cycling'); ?>
                    </a>
                    <a href="#tours" class="btn btn-secondary">
                        <?php _e('Explore Our Tours', 'cami-cycling'); ?>
                    </a>
                </div>
                
                <div class="error-search">
                    <h3><?php _e('Looking for something specific?', 'cami-cycling'); ?></h3>
                    <?php get_search_form(); ?>
                </div>
                
                <div class="error-links">
                    <h4><?php _e('Popular Destinations', 'cami-cycling'); ?></h4>
                    <ul>
                        <li><a href="#routes"><?php _e('Ribes de Freser & Camprodon', 'cami-cycling'); ?></a></li>
                        <li><a href="#routes"><?php _e('Montseny & Sant Hilari Sacalm', 'cami-cycling'); ?></a></li>
                        <li><a href="#routes"><?php _e('Garraf & Alt Penedès', 'cami-cycling'); ?></a></li>
                        <li><a href="#routes"><?php _e('Barcelona & Collserola', 'cami-cycling'); ?></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
</main>

<style>
.error-404 {
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.error-section {
    text-align: center;
    padding: 5rem 0;
}

.error-content {
    max-width: 600px;
    margin: 0 auto;
}

.error-geometric {
    margin-bottom: 2rem;
}

.error-404 h1 {
    font-size: 3rem;
    color: var(--stone-grey);
    margin-bottom: 1rem;
}

.error-subtitle {
    font-size: 1.2rem;
    color: var(--stone-grey-light);
    margin-bottom: 3rem;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 4rem;
    flex-wrap: wrap;
}

.error-search {
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(135, 169, 107, 0.05);
    border-radius: 12px;
}

.error-search h3 {
    margin-bottom: 1rem;
    color: var(--sage-green);
}

.error-search form {
    display: flex;
    gap: 1rem;
    justify-content: center;
    max-width: 400px;
    margin: 0 auto;
}

.error-search input[type="search"] {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(138, 137, 136, 0.2);
    border-radius: 6px;
    font-family: 'Montserrat', sans-serif;
}

.error-search input[type="submit"] {
    padding: 0.75rem 1.5rem;
    background: var(--sage-green);
    color: var(--warm-white);
    border: none;
    border-radius: 6px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.error-search input[type="submit"]:hover {
    background: var(--sage-green-hover);
}

.error-links {
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

.error-links h4 {
    color: var(--sage-green);
    margin-bottom: 1rem;
    text-align: center;
}

.error-links ul {
    list-style: none;
    padding: 0;
}

.error-links li {
    margin-bottom: 0.5rem;
}

.error-links a {
    color: var(--stone-grey);
    text-decoration: none;
    transition: color 0.3s ease;
}

.error-links a:hover {
    color: var(--sage-green);
}

@media (max-width: 768px) {
    .error-404 h1 {
        font-size: 2.5rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-search form {
        flex-direction: column;
    }
}
</style>

<?php get_footer(); ?>
