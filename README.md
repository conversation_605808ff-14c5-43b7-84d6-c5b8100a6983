# Camí Cycling Tours - WordPress Theme

A premium WordPress theme for cycling tour operators featuring Minimalist Landscape Geometry design concept. Specifically designed for Catalonian cycling tour operator targeting premium mid-range customers (€180-320/day pricing).

## Design Philosophy

**Core Concept:** "Minimalist Landscape Geometry" - sophisticated geometric abstraction of Catalonian landscapes with mathematical precision and timeless appeal.

**Brand Positioning:** Premium-authentic between budget operators and luxury providers.

## Color Palette

- **Primary:** Sage Green (#87a96b) - sophisticated green with grey undertones
- **Secondary:** <PERSON> Grey (#8a8988) - neutral anchor for text and accents  
- **Background:** Warm White (#fefdfb) - slightly warm tone, not pure white

## Features

### Design Elements
- Triangular geometric forms abstracting Catalonian mountains
- Montserrat typography with mathematical precision
- Responsive design with mobile-first approach
- Geometric patterns and mathematical spacing
- Premium aesthetic without luxury pricing

### Functionality
- **Multilingual Support:** English, Spanish, Russian, Catalan
- **Booking System:** Integrated contact form with email notifications
- **Service Tiers:** Three pricing levels with detailed descriptions
- **Routes Showcase:** Four destination cards with high-quality imagery
- **Testimonials:** Customer reviews with geometric styling
- **SEO Optimized:** Structured data and meta tags
- **Performance Optimized:** Fast loading times and optimized assets

### Sections
1. **Hero Section** - Full-viewport with geometric overlays
2. **Service Tiers** - Three-tier pricing structure
3. **Routes Showcase** - Four Catalonian destinations
4. **About Section** - Company story with statistics
5. **Testimonials** - Customer reviews
6. **Booking Section** - Comprehensive booking form
7. **Footer** - Contact information and social links

## Installation

### WordPress Installation

1. **Upload Theme Files:**
   ```
   wp-content/themes/cami-cycling-tours/
   ```

2. **Activate Theme:**
   - Go to WordPress Admin → Appearance → Themes
   - Find "Camí Cycling Tours" and click "Activate"

3. **Configure Settings:**
   - Set a static front page (Settings → Reading)
   - Configure permalinks (Settings → Permalinks)
   - Install recommended plugins (see below)

### Required WordPress Setup

1. **Create Pages:**
   - Homepage (set as static front page)
   - Tours
   - Routes  
   - About
   - Contact

2. **Configure Menus:**
   - Go to Appearance → Menus
   - Create primary navigation menu
   - Assign to "Primary Menu" location

3. **Customize Settings:**
   - Go to Appearance → Customize
   - Upload logo and favicon
   - Set site colors (if using customizer)

## Recommended Plugins

### Essential Plugins
- **Contact Form 7** - For advanced form handling
- **WPML** or **Polylang** - For multilingual functionality
- **Yoast SEO** - For enhanced SEO features
- **WP Rocket** - For performance optimization

### Optional Plugins
- **Advanced Custom Fields** - For custom content management
- **WooCommerce** - If selling tours directly online
- **Google Analytics** - For tracking and analytics
- **UpdraftPlus** - For backups

## Customization

### Colors
Edit the CSS custom properties in `style.css`:
```css
:root {
  --sage-green: #87a96b;
  --stone-grey: #8a8988;
  --warm-white: #fefdfb;
}
```

### Typography
The theme uses Montserrat font family. To change:
1. Update the Google Fonts import in `style.css`
2. Modify the font-family declarations

### Content Translation
The theme is translation-ready. To add languages:
1. Install WPML or Polylang plugin
2. Configure language settings
3. Translate content through the plugin interface

## File Structure

```
cami-cycling-tours/
├── style.css                 # Main stylesheet with theme header
├── index.php                 # Main template file
├── front-page.php            # Homepage template
├── header.php                # Header template
├── footer.php                # Footer template
├── functions.php             # Theme functions
├── assets/
│   ├── css/
│   │   └── additional-styles.css
│   ├── js/
│   │   └── main.js
│   └── images/
│       └── (placeholder images)
└── README.md
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- Optimized CSS and JavaScript
- Lazy loading for images
- Minimal HTTP requests
- Compressed assets
- SEO-friendly markup

## Support

For theme support and customization:
- Review the code comments for guidance
- Check WordPress Codex for standard functions
- Ensure all required plugins are installed and activated

## License

This theme is designed specifically for Camí Cycling Tours. Modify as needed for your specific requirements.

## Credits

- **Design Concept:** Minimalist Landscape Geometry
- **Typography:** Montserrat (Google Fonts)
- **Images:** Unsplash (placeholder images)
- **Icons:** Lucide React (geometric elements)

---

**Note:** This theme is designed for WordPress and requires a WordPress installation to function properly. Ensure you have the latest version of WordPress for optimal compatibility.
